import React from 'react';

import { Icon } from '@rneui/themed';
import { useTranslation } from 'react-i18next';
import { StyleSheet, View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { useThemeColorLegacy } from '@/hooks/useThemeColor';

export default function ComingSoon({ iconName }: { iconName: string }) {
  const { t } = useTranslation();
  const theme = useThemeColorLegacy();

  return (
    <View style={styles.container}>
      <Icon name={iconName} type="material" size={40} color={theme.theme} style={styles.icon} />
      <ThemedText h3 lightColor={theme.theme} darkColor={theme.theme} style={styles.title}>
        {t('comingSoon.title')}
      </ThemedText>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {
    marginBottom: 10,
  },
  title: {},
});
