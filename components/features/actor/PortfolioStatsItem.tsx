import React from 'react';

import { useTranslation } from 'react-i18next';
import { View } from 'react-native';

import { useUserProfile } from '@/common/profile';
import { ThemedText } from '@/components/global/ThemedText';
import { useThemeColorLegacy } from '@/hooks/useThemeColor';

export type PortfolioStatsItemProps = {
  title: string;
  value?: {
    amount: number;
    unit: string;
  };
  extraInfo?: React.ReactNode;
};

export default function PortfolioStatsItem({ title, value, extraInfo }: PortfolioStatsItemProps) {
  const { t } = useTranslation();
  const theme = useThemeColorLegacy();
  const unit = useUserProfile().profile?.flags.currency;

  return (
    <View className="mb-2 gap-3 flex flex-1 justify-between">
      <ThemedText style={{ color: theme.muted }}>{title}</ThemedText>
      <View className="flex flex-row justify-between items-end">
        <ThemedText className="text-2xl font-bold">
          {t('currency', {
            amount: {
              amount: value?.amount ?? 0,
              unit: value?.unit ?? unit,
              options: {
                notation: 'compact',
              },
            },
          })}
        </ThemedText>
        {!!extraInfo && (
          <ThemedText size={12} lightColor={theme.muted} darkColor={theme.muted} style={{ marginBottom: 3 }}>
            {extraInfo}
          </ThemedText>
        )}
      </View>
    </View>
  );
}
