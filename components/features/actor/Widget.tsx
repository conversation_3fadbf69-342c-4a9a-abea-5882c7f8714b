import React from 'react';

import { ActivityIndicator, StyleProp, View, ViewStyle } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { useThemeColorLegacy } from '@/hooks/useThemeColor';

type WidgetProps = {
  title?: string;
  children?: React.ReactNode;
  ready?: boolean;
  settings?: React.ReactNode;
  styles?: {
    container?: StyleProp<ViewStyle>;
    root?: StyleProp<ViewStyle>;
  };
};

export default function Widget({ title, children, ready, settings, styles }: WidgetProps) {
  const theme = useThemeColorLegacy();

  return (
    <View
      className="rounded-xl p-4 mb-6"
      style={[
        styles?.root,
        {
          backgroundColor: theme.backgroundSecondary,
        },
      ]}
    >
      {(!!title || !!settings) && (
        <View className="mb-4 flex flex-row justify-between items-center">
          <ThemedText h3 className="font-bold">
            {title}
          </ThemedText>
          {settings}
        </View>
      )}
      <View style={styles?.container}>{!ready ? <ActivityIndicator /> : children}</View>
    </View>
  );
}
