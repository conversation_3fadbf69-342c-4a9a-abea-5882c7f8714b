import React, { forwardRef, useRef } from 'react';

import { useFocusEffect } from 'expo-router';
import { ScrollView as NativeScrollView, ScrollViewProps } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { isHeaderVisible } from '@/signals/app.signal';

type Props = ScrollViewProps;

export type ScrollScreenRef = NativeScrollView;

export const ScrollScreen = forwardRef<NativeScrollView, Props>(
  ({ children, style, contentContainerStyle, ...props }, ref) => {
    const insets = useSafeAreaInsets();
    const scrollY = useRef<number>();
    const isFocused = useRef<boolean>(true);

    // Calculate padding top: header height (100px) + safe area top + some spacing (10px)
    const paddingTop = 100 + insets.top + 10;

    useFocusEffect(() => {
      isFocused.current = true;
      isHeaderVisible.value = (scrollY.current ?? 0) > 50;

      return () => {
        isFocused.current = false;
        isHeaderVisible.value = false;
      };
    });

    return (
      <NativeScrollView
        {...props}
        ref={ref}
        style={[
          {
            flex: 1,
            overflow: 'visible',
          },
          style,
        ]}
        contentContainerStyle={[
          {
            paddingTop: paddingTop,
            paddingHorizontal: 20,
            overflow: 'visible',
          },
          contentContainerStyle,
        ]}
        onScroll={event => {
          const y = event.nativeEvent.contentOffset.y;
          scrollY.current = y;

          if (!isFocused.current) return;

          if (y > 50) {
            isHeaderVisible.value = true;
          } else {
            isHeaderVisible.value = false;
          }
          props.onScroll?.(event);
        }}
      >
        {children}
      </NativeScrollView>
    );
  },
);
