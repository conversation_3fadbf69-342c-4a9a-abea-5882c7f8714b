import React from 'react';

import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { AppTheme } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';
import { useCurrentThemeColors } from '@/hooks/useThemeColor';

const THEME_OPTIONS: { label: string; value: AppTheme | 'system' }[] = [
  { label: 'System Default', value: 'system' },
  { label: 'Light Orange', value: 'lightOrange' },
  { label: 'Light Red', value: 'lightRed' },
  { label: 'Light Purple', value: 'lightPurple' },
  { label: 'Light Blue', value: 'lightBlue' },
  { label: 'Light Green', value: 'lightGreen' },
  { label: 'Dark Orange', value: 'darkOrange' },
  { label: 'Dark Red', value: 'darkRed' },
  { label: 'Dark Purple', value: 'darkPurple' },
  { label: 'Dark Blue', value: 'darkBlue' },
  { label: 'Dark Green', value: 'darkGreen' },
];

export function ThemeSelector() {
  const { theme, setTheme, currentTheme } = useTheme();
  const colors = useCurrentThemeColors();

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ThemedText size={24} type="bold" style={styles.title}>
        Choose Theme
      </ThemedText>
      <ThemedText size={14} style={styles.subtitle}>
        Current: {currentTheme} (Selected: {theme})
      </ThemedText>

      <ScrollView style={styles.scrollView}>
        {THEME_OPTIONS.map(option => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.themeOption,
              {
                backgroundColor: theme === option.value ? colors.primary : colors.secondary,
                borderColor: colors.border,
              },
            ]}
            onPress={() => setTheme(option.value)}
          >
            <ThemedText
              size={16}
              lightColor={theme === option.value ? colors.background : colors.text}
              darkColor={theme === option.value ? colors.background : colors.text}
              style={[
                styles.themeOptionText,
                {
                  fontWeight: theme === option.value ? 'bold' : 'normal',
                },
              ]}
            >
              {option.label}
            </ThemedText>
          </TouchableOpacity>
        ))}
      </ScrollView>

      <View style={[styles.previewContainer, { borderColor: colors.border }]}>
        <ThemedText size={18} type="bold" style={styles.previewTitle}>
          Theme Preview
        </ThemedText>
        <View style={styles.colorRow}>
          <View style={[styles.colorSwatch, { backgroundColor: colors.primary }]} />
          <ThemedText size={14} style={styles.colorLabel}>
            Primary
          </ThemedText>
        </View>
        <View style={styles.colorRow}>
          <View style={[styles.colorSwatch, { backgroundColor: colors.secondary }]} />
          <ThemedText size={14} style={styles.colorLabel}>
            Secondary
          </ThemedText>
        </View>
        <View style={styles.colorRow}>
          <View style={[styles.colorSwatch, { backgroundColor: colors.text }]} />
          <ThemedText size={14} style={styles.colorLabel}>
            Text
          </ThemedText>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  title: {
    marginBottom: 8,
  },
  subtitle: {
    marginBottom: 20,
    opacity: 0.7,
  },
  scrollView: {
    flex: 1,
    marginBottom: 20,
  },
  themeOption: {
    padding: 15,
    marginVertical: 5,
    borderRadius: 8,
    borderWidth: 1,
  },
  themeOptionText: {
    textAlign: 'center',
  },
  previewContainer: {
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
  },
  previewTitle: {
    marginBottom: 10,
  },
  colorRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
  },
  colorSwatch: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginRight: 10,
  },
  colorLabel: {},
});
