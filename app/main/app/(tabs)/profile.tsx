import React from 'react';

import { useTranslation } from 'react-i18next';
import { StyleSheet } from 'react-native';

import { SafeAreaView } from '@/components/base';
import { ThemedText } from '@/components/global/ThemedText';

import SettingsView from '../../settings';

export default function profile() {
  const { t } = useTranslation();

  return (
    <SafeAreaView>
      <ThemedText h1 className="font-bold" style={{ marginHorizontal: 25 }}>
        {t('settings.pages.settings')}
      </ThemedText>
      <SettingsView />
    </SafeAreaView>
  );
}
const styles = StyleSheet.create({});
